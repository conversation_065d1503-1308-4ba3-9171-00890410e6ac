# 固定场景集实现 - 工作记录

## 任务概述
- **问题**：评估时环境的初始状态（智能体位置）随机，导致评估任务难度不同，分数波动大
- **证据**：`auto_mst_rl_xuance/envs/mappo/ce_env.py:94` 的 `init_sim_robots` 使用 "unif" (均匀)分布随机初始化
- **解决方案**：创建固定测试场景集（10个固定初始位置配置），每次评估在相同场景上运行取平均
- **附加要求**：训练过程也使用固定场景，但场景数目为30个，测试使用前10个

## 实施计划
1. 分析当前初始化方法的代码实现
2. 设计固定场景生成和存储方案
3. 修改环境代码以支持固定场景
4. 更新训练和评估代码
5. 整合并测试修改

## 代码分析

### 1. 当前环境初始化分析

根据 `auto_mst_rl_xuance/envs/mappo/ce_env.py` 和 `auto_mst_rl_xuance/envs/sim_py/ce/tools/init_simulation.py` 的代码分析：

- `CEEnv` 在 `_reset_simulation` 方法中调用 `init_sim_robots`
- 当前使用 "unif" 模式（均匀分布）初始化，代码如下：
  ```python
  self.runtime_state = init_sim_robots(
      self.runtime_state,
      "unif",  # 均匀分布初始化
      np.array([0, 0]),  # 中心位置
      100,  # 分布半径
      True,  # 并行仿真
  )
  ```
- `init_sim_robots` 实现在 `init_simulation.py` 中，它通过 `randpose_unif` 函数生成随机位置
- 每次环境重置都会重新随机初始化，导致每次评估的场景不同

### 2. 训练与评估流程分析

从 `auto_mst_rl_xuance/trainer/mappo/train_mst_ce.py` 可以看出：

- 训练通过 `Agents.train()` 方法进行
- 评估通过 `Agents.test()` 方法进行，该方法会创建新的环境
- 环境创建使用 `make_envs` 函数，接收配置参数

## 实现方案

### 1. 创建固定场景管理模块

首先，需要创建一个模块来生成和管理固定场景：

**文件路径**：`auto_mst_rl_xuance/envs/sim_py/ce/tools/fixed_scenarios.py`

```python
import numpy as np
import os
import pickle
from pathlib import Path

class FixedScenarioManager:
    """管理固定场景的生成和加载"""
    
    def __init__(self, scenario_dir=None):
        """
        初始化场景管理器
        
        Args:
            scenario_dir: 场景文件存储目录，默认为项目根目录下的 'fixed_scenarios'
        """
        if scenario_dir is None:
            # 使用默认路径
            project_root = Path(__file__).parents[5]  # 获取项目根目录
            self.scenario_dir = project_root / 'fixed_scenarios'
        else:
            self.scenario_dir = Path(scenario_dir)
            
        # 确保目录存在
        os.makedirs(self.scenario_dir, exist_ok=True)
        
    def generate_scenarios(self, num_scenarios=30, max_id=50, seed=None):
        """
        生成固定数量的场景并保存
        
        Args:
            num_scenarios: 要生成的场景数量
            max_id: 每个场景中的智能体数量
            seed: 随机种子，确保可重复性
            
        Returns:
            scenarios: 生成的场景列表
        """
        if seed is not None:
            np.random.seed(seed)
            
        scenarios = []
        for i in range(num_scenarios):
            # 为每个场景生成位置，使用 randpose_unif 函数
            from auto_mst_rl_xuance.envs.sim_py.ce.tools.utils import randpose_unif
            positions, _ = randpose_unif(max_id)
            
            # 每个场景是一个字典，包含位置和元数据
            scenario = {
                'positions': positions,
                'metadata': {
                    'scenario_id': i,
                    'max_id': max_id,
                    'generated_time': np.datetime64('now')
                }
            }
            scenarios.append(scenario)
            
        # 保存场景到文件
        scenario_file = self.scenario_dir / f'fixed_scenarios_{num_scenarios}_{max_id}.pkl'
        with open(scenario_file, 'wb') as f:
            pickle.dump(scenarios, f)
            
        print(f"已生成并保存 {num_scenarios} 个固定场景到 {scenario_file}")
        return scenarios
    
    def load_scenarios(self, num_scenarios=None, max_id=50):
        """
        加载固定场景
        
        Args:
            num_scenarios: 要加载的场景数量，None表示加载所有场景
            max_id: 场景中的智能体数量
            
        Returns:
            scenarios: 加载的场景列表，如果文件不存在则返回None
        """
        # 确定场景文件名
        total_scenarios = 30  # 默认总场景数
        scenario_file = self.scenario_dir / f'fixed_scenarios_{total_scenarios}_{max_id}.pkl'
        
        if not scenario_file.exists():
            print(f"场景文件 {scenario_file} 不存在，将自动生成")
            scenarios = self.generate_scenarios(total_scenarios, max_id)
        else:
            # 加载场景文件
            with open(scenario_file, 'rb') as f:
                scenarios = pickle.load(f)
                
        # 如果指定了场景数量，只返回指定数量的场景
        if num_scenarios is not None:
            scenarios = scenarios[:num_scenarios]
            
        return scenarios
```

### 2. 添加支持固定位置的初始化函数

在 `auto_mst_rl_xuance/envs/sim_py/ce/tools/init_simulation.py` 中添加一个新函数：

```python
def init_sim_robots_with_fixed_positions(
    runtime_state: RuntimeState,
    positions: np.ndarray,  # 预生成的位置数组
    pos_offset: np.ndarray,
    r: float,
    is_parallel_simulation: bool = False,
) -> RuntimeState:
    """
    使用固定位置初始化虚拟智能体
    
    Args:
        runtime_state: 运行时状态
        positions: 预生成的位置数组，形状为 (2, n)，第一行是x坐标，第二行是y坐标
        pos_offset: 位置偏移
        r: 半径
        is_parallel_simulation: 是否进行平行仿真
        
    Returns:
        更新后的运行时状态
    """
    config = runtime_state.config
    # 初始化机器人的数量及全体编号列表
    runtime_state.robots_list = list(range(1, runtime_state.max_id + 1))
    robots_num = runtime_state.max_id
    
    # 初始化位置和速度数组
    robots_pos_with_dir = np.zeros((robots_num, 4))
    
    # 使用预生成的位置
    robots_pos_with_dir[:, 0] = r * positions[0, :] - pos_offset[0]
    robots_pos_with_dir[:, 1] = r * positions[1, :] - pos_offset[1]
    # 方向小范围随机
    robots_pos_with_dir[:, 2:] = 0.1 * (0.5 - np.random.rand(robots_num, 2))
    
    # 与原函数相同的特殊处理逻辑
    if (
        is_parallel_simulation
        and config.hawk_id is not None
    ):
        predator_pos_for_sorting = np.array([2000, 2000])
        
        distances = np.sqrt(
            np.sum(
                (robots_pos_with_dir[:, :2] - predator_pos_for_sorting) ** 2, axis=1
            )
        )
        idx_closest_to_ref = np.argmin(distances)
        
        info_agent_id_to_place = 2
        if info_agent_id_to_place <= robots_num and info_agent_id_to_place != (
            idx_closest_to_ref + 1
        ):
            temp_row = robots_pos_with_dir[info_agent_id_to_place - 1, :].copy()
            robots_pos_with_dir[info_agent_id_to_place - 1, :] = (
                robots_pos_with_dir[idx_closest_to_ref, :].copy()
            )
            robots_pos_with_dir[idx_closest_to_ref, :] = temp_row
    
    # 记录机器人的实时运动状态
    for i in range(1, robots_num + 1):
        agent_idx_in_array = i - 1  # 数组索引从0开始，但Agent ID从1开始
        
        # 确定初始速度：普通个体固定，攻击者特定
        initial_vel_for_agent = unitvel(np.array([1, 1]))  # 普通个体的默认值
        
        agent = Agent(
            id=i,
            pose=np.array(robots_pos_with_dir[agent_idx_in_array, :2]),
            vel=initial_vel_for_agent,  # 应用确定的初始速度
            is_activated=False,
            src_id=None,
            cj_threshold=config.cj_threshold,  # 从 SimulationConfig 获取
        )
        runtime_state.actors[i] = agent
    
    # 如果有捕食者，设置其位置和速度
    if config.hawk_id is not None:
        hawk_initial_pose = np.array([2000, 2000])
        hawk_initial_vel = np.array([-1, -1])
        
        runtime_state.actors[config.hawk_id] = Agent(
            id=config.hawk_id,
            pose=hawk_initial_pose,
            vel=hawk_initial_vel,
            is_activated=False,
            src_id=None,
            cj_threshold=config.cj_threshold,
        )
    
    return runtime_state
```

### 3. 修改环境类支持固定场景

修改 `auto_mst_rl_xuance/envs/mappo/ce_env.py` 中的 `CEEnv` 类：

```python
# 添加导入
from auto_mst_rl_xuance.envs.sim_py.ce.tools.fixed_scenarios import FixedScenarioManager
from auto_mst_rl_xuance.envs.sim_py.ce.tools.init_simulation import init_sim_robots_with_fixed_positions

class CEEnv(MSTEnv):
    def __init__(
        self,
        config: dict,
        max_episode_steps: int = 1200,
        enable_visualization: bool = False,
        use_fixed_scenarios: bool = False,  # 新增参数
        num_fixed_scenarios: int = 30,      # 新增参数
        is_evaluation: bool = False,        # 新增参数
    ):
        """
        初始化CE环境
        
        参数:
            ...原有参数...
            use_fixed_scenarios (bool): 是否使用固定场景
            num_fixed_scenarios (int): 固定场景数量
            is_evaluation (bool): 是否为评估模式，决定使用哪些场景
        """
        # 固定场景相关设置
        self.use_fixed_scenarios = use_fixed_scenarios
        self.num_fixed_scenarios = num_fixed_scenarios
        self.is_evaluation = is_evaluation
        
        # 初始化基类
        super(CEEnv, self).__init__(config, max_episode_steps)
        
        # 设置场景类型编码
        self.scenario_type_code = 0  # Chase-Escape场景
        
        # 实时可视化相关设置
        self.enable_visualization = enable_visualization
        self.visualization_fig = None
        self.visualization_axes = None
        
        # 加载固定场景（如果需要）
        if self.use_fixed_scenarios:
            self.scenario_manager = FixedScenarioManager()
            if self.is_evaluation:
                # 评估模式使用前10个场景
                self.fixed_scenarios = self.scenario_manager.load_scenarios(10, getattr(self.config, "max_id", 50))
            else:
                # 训练模式使用所有场景
                self.fixed_scenarios = self.scenario_manager.load_scenarios(self.num_fixed_scenarios, getattr(self.config, "max_id", 50))
            
            # 当前场景索引，用于循环使用场景
            self.current_scenario_idx = 0
        
        # 初始化仿真环境
        self._reset_simulation()
        
        # 确保agents列表在初始化时正确设置
        if not hasattr(self, "agents") or not self.agents:
            self.agents = [str(i) for i in self.runtime_state.robots_list]
            
        # 确保n_agents属性正确设置
        if not hasattr(self, "n_agents")