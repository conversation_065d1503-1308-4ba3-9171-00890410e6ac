# Flocking Pattern场景的MAPPO算法配置
# 该配置文件适用于Flocking Pattern场景的多智能体强化学习任务
# 基础设置
dl_toolbox: "torch"  # 深度学习框架: "torch", "mindspore", "tensorlayer"
project_name: "MST_RL"
logger: "tensorboard"  # 日志工具: "tensorboard", "wandb"
wandb_user_name: "none"
test_mode: false
device: "cuda:0"  # 计算设备: "cpu", "cuda:0"
distributed_training: false  # 是否使用分布式训练
master_port: "12355"  # 分布式训练主端口
clip_type: 1  # 梯度裁剪类型

# 环境设置
env_name: "FPEnv"  # 环境名称
env_id: "FPEnv"  # 环境ID
env_seed: 42  # 环境随机种子
continuous_action: true  # 连续动作空间
render: false  # 是否开启渲染
render_mode: null
fps: 30  # 渲染帧率
vectorize: "DummyVecMultiAgentEnv"  # 向量化环境类型
running_steps: 1000000  # 总运行步数
eval_interval: 5000  # 评估间隔
test_episode: 5  # 测试轮数

# 环境特定参数
max_id: 50  # 智能体数量
max_episode_steps: 5000  # 最大步数（注意FP场景更长）
mst_min: 0.0  # MST阈值的最小值
mst_max: 35.0  # MST阈值的最大值
k_neighbors: 7  # 观察中考虑的邻居数量
normalize_obs: true  # 归一化观察
normalize_state: true  # 归一化状态

# 算法设置
algo_name: "MAPPO"
learner: "MAPPO_Clip_Learner"
policy: "Gaussian_MAAC_Policy"  # 连续动作的高斯策略
representation: "Basic_MLP"

# 网络架构设置
representation_args:  # 表示网络参数
  hidden_sizes: [256, 128]
  activation: "relu"
representation_hidden_size: [256, 128]  # 表示网络隐藏层大小（与XuanCe库兼容）
actor_hidden_size: [128, 128]  # Actor网络隐藏层大小
critic_hidden_size: [128, 128]  # Critic网络隐藏层大小
activation: "relu"  # 激活函数
activation_action: "tanh"  # 动作输出层的激活函数
use_parameter_sharing: true  # 是否使用参数共享
use_actions_mask: false  # 是否使用动作掩码

# RNN设置（当使用RNN时）
use_rnn: false  # 是否使用循环神经网络
rnn: "GRU"  # 循环层类型: "GRU", "LSTM"
N_recurrent_layers: 1  # 循环层数量
recurrent_hidden_size: 64  # 循环层隐藏单元数量
dropout: 0  # 丢弃率
normalize: "LayerNorm"  # 层归一化
initialize: "orthogonal"  # 网络初始化方式
gain: 0.01  # 初始化增益

# 训练参数
seed: 1  # 随机种子
parallels: 16  # 并行环境数量
buffer_size: 4096  # 每个并行环境的缓冲区大小（FP场景用更大的缓冲区）
n_epochs: 10  # 每批数据的训练次数
n_minibatch: 2  # 小批量数量
learning_rate: 3.0e-4  # 学习率（FP场景学习率略低）
lr_a: 3.0e-4  # Actor学习率
lr_c: 3.0e-4  # Critic学习率
weight_decay: 0  # 权重衰减
lr_clip_range: [1.0e-6, 1.0e-6]  # 学习率裁剪范围

# PPO参数
vf_coef: 0.5  # 价值函数系数
ent_coef: 0.005  # 熵系数（FP场景熵系数较小）
target_kl: 0.015  # KL散度目标
clip_range: 0.2  # PPO裁剪范围
gamma: 0.995  # 折扣因子（FP场景折扣因子较大）

# 技巧选项
use_linear_lr_decay: false  # 是否使用线性学习率衰减
end_factor_lr_decay: 0.5  # 学习率衰减结束因子
use_global_state: true  # 是否使用全局状态计算价值
use_value_clip: true  # 是否限制价值范围
value_clip_range: 0.2  # 价值裁剪范围
use_value_norm: true  # 是否使用奖励归一化
use_huber_loss: true  # 是否使用huber损失
huber_delta: 10.0  # huber损失delta参数
use_advnorm: true  # 是否使用优势归一化
use_gae: true  # 是否使用GAE计算累积奖励
gae_lambda: 0.95  # GAE lambda参数
use_grad_clip: true  # 是否使用梯度裁剪
grad_clip_norm: 10.0  # 梯度裁剪范数

# 日志和保存
use_wandb: false  # 是否使用wandb记录
log_dir: "./logs/mappo/fp/"  # 日志目录
model_dir: "./models/mappo/fp/"  # 模型保存目录 