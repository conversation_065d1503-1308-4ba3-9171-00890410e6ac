from typing import Optional  # 在此处添加了 Optional

import numpy as np

from auto_mst_rl_xuance.envs.sim_py.ce.tools.agent_utils import (
    Agent,
    MetricsCollector,
    RuntimeState,
    SimulationConfig,
)
from auto_mst_rl_xuance.envs.sim_py.ce.tools.utils import randpose_unif, unitvel


def init_sim_robots(
    runtime_state: RuntimeState,
    rand_mode: str,
    pos_offset: np.ndarray,
    r: float,
    is_parallel_simulation: bool = False,  # TODO: 确认此参数的实际用途并考虑重命名
) -> RuntimeState:
    """
    初始化虚拟智能体

    Args:
        runtime_state: 运行时状态
        rand_mode: 随机模式，'rand'或'unif'
        pos_offset: 位置偏移
        r: 半径
        is_parallel_simulation: 是否进行平行仿真 (TODO: 审视此参数)

    Returns:
        更新后的运行时状态
    """
    config = runtime_state.config
    # 初始化机器人的数量及全体编号列表
    runtime_state.robots_list = list(range(1, runtime_state.max_id + 1))
    robots_num = runtime_state.max_id

    # 初始化位置和速度数组
    # robots_pos 的第3、4列用于临时存储方向，之后转换为vel
    robots_pos_with_dir = np.zeros((robots_num, 4))

    # 根据不同模式初始化位置和方向
    if rand_mode == "rand":
        # 位置和方向完全随机，可能重叠
        robots_pos_with_dir[:, :2] = r * np.random.rand(robots_num, 2)
        robots_pos_with_dir[:, 2:] = 0.5 - np.random.rand(robots_num, 2)
    elif rand_mode == "unif":
        # 基本均匀的随机分布，不会重叠
        # randpose_unif 从 utils.py 导入
        positions, _ = randpose_unif(robots_num)  # 这是重复的函数，现在已导入
        robots_pos_with_dir[:, 0] = r * positions[0, :] - pos_offset[0]
        robots_pos_with_dir[:, 1] = r * positions[1, :] - pos_offset[1]
        robots_pos_with_dir[:, 2:] = 0.1 * (
            0.5 - np.random.rand(robots_num, 2)
        )  # 方向小范围随机

        # 如果有捕食者且需要平行仿真，进行捕食者相关处理
        # TODO: "is_parallel_simulation" 参数的含义和此逻辑块的目的需要进一步澄清
        # 如果此逻辑是为了将某个特定ID的agent（如信息传递者）放置在特殊位置，
        # 应使其更通用或通过配置驱动。
        if (
            is_parallel_simulation
            and config.hawk_id is not None  # hawk_id 在 SimulationConfig 中
        ):
            # 捕食者的位置应当在外部设置，这里不做处理，或者从config读取
            # 此处的 predator_pos 仅用于计算距离以重新排列某个agent
            # 这个逻辑比较特殊，假设是为了测试或特定场景
            predator_pos_for_sorting = np.array([2000, 2000])  # 默认参考位置

            # 计算每个位置到参考捕食者位置的距离
            distances = np.sqrt(
                np.sum(
                    (robots_pos_with_dir[:, :2] - predator_pos_for_sorting) ** 2, axis=1
                )
            )
            # 找到最近的位置的索引
            idx_closest_to_ref = np.argmin(distances)

            # 将2号个体（通常假设为信息个体）设置为离参考点最近的位置
            # TODO: "2号个体"这个硬编码需要参数化或配置化
            info_agent_id_to_place = 2
            if info_agent_id_to_place <= robots_num and info_agent_id_to_place != (
                idx_closest_to_ref + 1
            ):  # 确保ID有效且不是同一个
                # 交换位置和方向数据
                temp_row = robots_pos_with_dir[info_agent_id_to_place - 1, :].copy()
                robots_pos_with_dir[info_agent_id_to_place - 1, :] = (
                    robots_pos_with_dir[idx_closest_to_ref, :].copy()
                )
                robots_pos_with_dir[idx_closest_to_ref, :] = temp_row

    # 记录机器人的实时运动状态
    for i in range(1, robots_num + 1):
        agent_idx_in_array = i - 1  # 数组索引从0开始，但Agent ID从1开始

        # 确定初始速度：普通个体固定，攻击者特定
        initial_vel_for_agent = unitvel(np.array([1, 1]))  # 普通个体的默认值

        agent = Agent(
            id=i,
            pose=np.array(robots_pos_with_dir[agent_idx_in_array, :2]),
            vel=initial_vel_for_agent,  # 应用确定的初始速度
            is_activated=False,
            src_id=None,
            cj_threshold=config.cj_threshold,  # 从 SimulationConfig 获取
        )
        runtime_state.actors[i] = agent

    # 如果有捕食者，设置其位置和速度
    if config.hawk_id is not None:
        # 创建或更新捕食者
        # TODO: 捕食者的初始位置和速度也应该从config中读取或参数化
        hawk_initial_pose = np.array([2000, 2000])  # 硬编码
        hawk_initial_vel = np.array([-1, -1])  # 硬编码

        # 确保不会覆盖 robots_list 中已有的非捕食者 agent
        # 如果 hawk_id 与某个普通 agent_id 冲突，需要明确处理策略
        # 假设 hawk_id 是唯一的，或者如果它替换了一个已有的 id，那是故意的
        runtime_state.actors[config.hawk_id] = Agent(
            id=config.hawk_id,
            pose=hawk_initial_pose,
            vel=hawk_initial_vel,
            is_activated=False,  # 捕食者通常不参与被激活逻辑
            src_id=None,
            cj_threshold=config.cj_threshold,  # 捕食者也可能有cj_threshold?
        )
        # 确保捕食者也在robots_list中，如果它还没有的话
        if config.hawk_id not in runtime_state.robots_list:
            # 这通常意味着 max_id 可能没有包含 hawk_id，或者 hawk_id 是一个特殊的 agent
            # 如果 hawk_id > max_id，robots_list 的创建方式需要调整
            # 假设 hawk_id <= max_id 且应该在 robots_list 中
            # 如果 robots_list 是严格的 1..max_id, 且 hawk_id 在此范围内，则它已包含
            # 如果 hawk_id 是一个额外的 agent，那么 robots_list 和 max_id 的定义需要更灵活
            # 当前假设：如果 hawk_id 在 1..max_id 范围内，它已经是 robots_list 的一部分
            # 如果 hawk_id 是一个全新的、不在此范围内的 ID，则需要添加到 robots_list
            # 并且 max_id 的语义可能需要重新考虑（例如，它是“非捕食者最大ID”还是“总智能体数”）
            # 为简单起见，目前不修改 robots_list，假设 hawk_id 的处理在其他地方或通过配置保证
            pass

    return runtime_state


def create_initial_runtime_state(
    max_id: int = 50,
    cyc_time: float = 0.2,
    v0: float = 12,
    max_rot_rate: float = 12 * 1.91,
    r_sense: float = 1000,
    align_type: str = "ave",
    weight_align: float = 10,
    weight_rep: float = 8,
    weight_att: float = 0.01,
    weight_cj: float = 200,
    cj_threshold_val: float = 1,  # 已重命名以避免与 SimulationConfig 字段名冲突
    deac_threshold: float = 0.2,
    noise_mov: float = 0,
    max_neighbors: int = 7,
    drep: float = 300,
    dsen: float = 1000,
    weight_esc: float = 1,
    hawk_id_val: int = 1,  # 已重命名
    attack_step: int = 1,
    r_dead: float = 120,
    v0_hawk: float = 24,
    max_sim_steps_val: int = 1200,  # 已重命名
    r_escape_values: Optional[np.ndarray] = None,
) -> RuntimeState:
    """
    初始化并返回运行时状态，包括配置和度量收集器。

    Args:
        max_id: 最大智能体数量 (通常指非捕食者)
        cyc_time: 循环周期
        v0: 基础速度
        ... (其他配置参数)
        max_sim_steps_val: 最大仿真步数
        r_escape_values: 预设的逃逸半径数组, 长度应为 max_id

    Returns:
        初始化的运行时状态
    """
    if r_escape_values is None:
        # 默认的 r_escape 初始化逻辑
        r_escape_arr = 300 + np.zeros(max_id)
        if max_id >= 2:  # 确保ID为2的个体存在 (基于0索引，ID 2是索引1)
            r_escape_arr[1] = 1000  # 信息个体的预警半径 (索引1对应ID 2)
    else:
        r_escape_arr = r_escape_values
        if len(r_escape_arr) != max_id:
            raise ValueError(
                f"r_escape_values length ({len(r_escape_arr)}) must be equal to max_id ({max_id})"
            )

    sim_config = SimulationConfig(
        cyc_time=cyc_time,
        v0=v0,
        max_rot_rate=max_rot_rate,
        r_sense=r_sense,
        align_type=align_type,
        weight_align=weight_align,
        weight_rep=weight_rep,
        weight_att=weight_att,
        weight_cj=weight_cj,
        cj_threshold=cj_threshold_val,  # 使用重命名后的参数
        deac_threshold=deac_threshold,
        noise_mov=noise_mov,
        max_neighbors=max_neighbors,
        drep=drep,
        dsen=dsen,
        weight_esc=weight_esc,
        hawk_id=hawk_id_val,  # 使用重命名后的参数
        attack_step=attack_step,
        r_dead=r_dead,
        v0_hawk=v0_hawk,
        r_escape=r_escape_arr,  # 使用处理后的 r_escape_arr
        max_sim_steps=max_sim_steps_val,  # 使用重命名后的参数
    )

    metrics_collector = MetricsCollector()
    # 根据配置的 max_sim_steps 和传递的 max_id 初始化度量
    metrics_collector.initialize_metrics(sim_config.max_sim_steps, max_id)

    runtime_state = RuntimeState(
        max_id=max_id,  # 运行时状态的 max_id，主要用于非攻击者个体的数量
        config=sim_config,
        metrics=metrics_collector,
        sim_step=0,
        actors={},  # actors 将在 init_sim_robots 中填充
        robots_list=[],  # robots_list 将在 init_sim_robots 中填充
    )

    return runtime_state
