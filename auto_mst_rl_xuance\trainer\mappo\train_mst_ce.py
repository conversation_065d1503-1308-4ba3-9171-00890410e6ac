import argparse
import os
import platform
import time
from copy import deepcopy

import numpy as np

# 导入自定义环境
import wandb
import yaml

# #############################################
# Windows兼容性补丁
if platform.system() == "Windows":
    original_init = wandb.init

    def patched_init(*args, **kwargs):
        if "settings" in kwargs and hasattr(kwargs["settings"], "start_method"):
            kwargs["settings"].start_method = "spawn"
        return original_init(*args, **kwargs)

    wandb.init = patched_init
# #############################################
from auto_mst_rl_xuance.envs import register_envs
from xuance.common import recursive_dict_update
from xuance.environment import make_envs
from xuance.torch.agents import MAPPO_Agents
from xuance.torch.utils.operations import set_seed


def load_config(file_path):
    """加载YAML配置文件，指定UTF-8编码"""
    with open(file_path, "r", encoding="utf-8") as f:
        config_dict = yaml.load(f, Loader=yaml.FullLoader)

    # 根据服务器核心数覆盖parallels参数
    cpu_cores = os.cpu_count()
    if cpu_cores is not None:
        config_dict["parallels"] = int(cpu_cores)
        print(f"动态设置并行环境数量为: {int(cpu_cores)} (服务器核心数)")
    else:
        print("未能获取服务器核心数，将使用配置文件中的默认值。")

    return config_dict


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser("基于XuanCe的自适应MST-CE场景训练")
    parser.add_argument("--test", type=int, default=0, help="是否测试模式")
    parser.add_argument("--benchmark", type=int, default=1, help="是否基准测试模式")
    parser.add_argument(
        "--config", type=str, default="ce_mappo_config.yaml", help="配置文件"
    )

    return parser.parse_args()


if __name__ == "__main__":
    # 初始化变量，便于在 finally 块中访问
    envs = None
    Agents = None

    try:
        # 注册自定义环境
        register_envs()

        # 解析参数
        parser = parse_args()

        # 获取当前脚本的目录路径 (.../auto_mst_rl_xuance/trainer/mappo)
        current_script_dir = os.path.dirname(os.path.abspath(__file__))
        # 定位到 auto_mst_rl_xuance 目录
        project_root_auto_mst_rl_xuance = os.path.abspath(
            os.path.join(current_script_dir, "..", "..")
        )
        # 构建配置文件的正确路径 (e.g., .../auto_mst_rl_xuance/configs/mappo/ce_mappo_config.yaml)
        config_path = os.path.join(
            project_root_auto_mst_rl_xuance, "configs", "mappo", parser.config
        )

        # 加载配置
        configs_dict = load_config(config_path)
        configs_dict = recursive_dict_update(configs_dict, parser.__dict__)

        # 确保配置项命名正确
        if "n_epoch" in configs_dict and "n_epochs" not in configs_dict:
            configs_dict["n_epochs"] = configs_dict.pop("n_epoch")

        configs = argparse.Namespace(**configs_dict)

        # 设置随机种子
        set_seed(configs.seed)

        # 创建环境
        envs = make_envs(configs)
        print("已注册环境...")

        # 尝试主动调用一次reset
        obs, info = envs.reset()

        # 手动设置agents属性（从observation_space的键中获取）
        if not hasattr(envs, "agents") or not envs.agents:  # 如果agents属性不存在或为空
            envs.agents = list(envs.observation_space.keys())
            print(f"手动设置智能体列表: {envs.agents}")

        # 确保环境有正确的num_agents属性
        if not hasattr(envs, "num_agents") or envs.num_agents is None:
            envs.num_agents = len(envs.agents)
            print(f"手动设置num_agents属性: {envs.num_agents}")

        # 确保向量化环境的每个子环境也有正确的num_agents属性
        if hasattr(envs, "venv") and hasattr(envs.venv, "envs"):
            for env_idx, env in enumerate(envs.venv.envs):
                if not hasattr(env, "num_agents") or env.num_agents is None:
                    env.num_agents = len(envs.agents)
                    print(f"修复子环境 {env_idx} 的num_agents属性: {env.num_agents}")

        print(f"智能体数量: {envs.num_agents}")

        # 创建MAPPO智能体
        Agents = MAPPO_Agents(config=configs, envs=envs)
        # 如果设置了继续训练，并且不是测试模式，则加载模型
        if hasattr(configs, "continue_training") and configs.continue_training:
            if not configs.test:  # 仅在非测试模式下应用此继续训练逻辑
                if hasattr(Agents, "model_dir_load") and Agents.model_dir_load:
                    print(
                        f"INFO: 训练模式 - 尝试从目录 '{Agents.model_dir_load}' 加载模型。"
                    )
                    try:
                        Agents.load_model(path=Agents.model_dir_load)
                        print(f"INFO: 模型从目录 '{Agents.model_dir_load}' 加载成功。")
                    except Exception as e:
                        print(
                            f"WARNING: 从目录 '{Agents.model_dir_load}' 加载模型失败: {e}。将从头开始训练。"
                        )
                else:
                    print(
                        "WARNING: 训练模式 - model_dir_load 未设置，无法加载模型。将从头开始训练。"
                    )

        # 添加训练监控变量
        episode_rewards = []
        episode_mst_values = []
        print_interval = 10  # 每多少个episodes打印一次统计信息

        # 注册回调函数来收集episode信息
        def episode_callback(episode_info):
            """每个episode结束时的回调函数"""
            if "episode_score" in episode_info:
                # 收集奖励信息
                for agent_id, reward in episode_info["episode_score"].items():
                    episode_rewards.append(reward)

                # 仅保留最近的100个episode
                if len(episode_rewards) > 100:
                    episode_rewards.pop(0)

                # 每隔一定数量的episodes打印一次信息
                if len(episode_rewards) % print_interval == 0:
                    avg_reward = np.mean(episode_rewards[-print_interval:])
                    print(f"最近{print_interval}个episodes的平均奖励: {avg_reward:.4f}")

        # 设置回调函数（这里假设XuanCe支持这种方式，如果不支持需要定制化）
        if hasattr(Agents, "set_episode_callback"):
            Agents.set_episode_callback(episode_callback)

        # 打印训练信息
        train_information = {
            "深度学习框架": configs.dl_toolbox,
            "计算设备": configs.device,
            "算法": configs.algo_name,
            "环境": configs.env_name,
            "场景": configs.env_id,
            "智能体数量": configs.max_id,
            "MST范围": f"[{configs.mst_min}, {configs.mst_max}]",
        }

        for k, v in train_information.items():
            print(f"{k}: {v}")

        # 基准测试模式
        if configs.benchmark:

            def env_fn():
                configs_test = deepcopy(configs)
                configs_test.parallels = configs_test.test_episode
                return make_envs(configs_test)

            train_steps = configs.running_steps // configs.parallels
            eval_interval = configs.eval_interval // configs.parallels
            test_episode = configs.test_episode
            num_epoch = int(train_steps / eval_interval)

            # 创建MST值监控函数
            avg_mst_values = []

            # 自定义步骤结束处理函数
            def custom_step_end_handler(step_info):
                """每个训练步骤结束时的处理函数"""
                # 减少MST值输出频率，每500步收集一次
                if step_info["current_step"] % 500 == 0:
                    # 获取当前环境中的所有MST值
                    try:
                        mst_values = []
                        for env_idx in range(configs.parallels):
                            for agent_id in Agents.envs.agents:
                                # 从环境中获取当前的MST阈值
                                actual_id = int(agent_id)
                                # 这里需要根据实际环境结构调整
                                try:
                                    mst_value = (
                                        Agents.envs.venv.envs[env_idx]
                                        .runtime_state.actors[actual_id]
                                        .cj_threshold
                                    )
                                    mst_values.append(mst_value)
                                except (AttributeError, IndexError, KeyError):
                                    pass

                        if mst_values:
                            avg_mst = np.mean(mst_values)
                            # 记录到TensorBoard
                            Agents.writer.add_scalar(
                                "MST/avg_threshold", avg_mst, step_info["current_step"]
                            )
                            # 记录标准差
                            std_mst = np.std(mst_values)
                            Agents.writer.add_scalar(
                                "MST/std_threshold", std_mst, step_info["current_step"]
                            )
                            # 记录最大最小值
                            Agents.writer.add_scalar(
                                "MST/max_threshold",
                                np.max(mst_values),
                                step_info["current_step"],
                            )
                            Agents.writer.add_scalar(
                                "MST/min_threshold",
                                np.min(mst_values),
                                step_info["current_step"],
                            )

                            avg_mst_values.append(avg_mst)

                            if len(avg_mst_values) > 50:
                                avg_mst_values.pop(0)

                            print(
                                f"步骤 {step_info['current_step']} - 平均MST值: {avg_mst:.4f}"
                            )
                    except Exception:
                        pass  # 不再打印错误信息，减少输出

            # 添加自定义训练处理函数（如果XuanCe支持）
            if hasattr(Agents, "set_step_end_handler"):
                Agents.set_step_end_handler(custom_step_end_handler)

            # 添加提示信息，让用户知道测试过程正在进行
            print("测试过程中没有详细的进度显示，请耐心等待...")

            # 初始测试
            print("开始进行初始测试...")
            # 直接使用XuanCe库内置的测试方法
            test_scores = Agents.test(env_fn, test_episode)
            valid_scores = test_scores[test_episode:]
            print("valid_scores:", valid_scores)
            print(
                f"测试得分: 平均={np.mean(valid_scores):.4f}, 标准差={np.std(valid_scores):.4f}"
            )

            Agents.save_model(model_name="best_model.pth")
            best_scores_info = {
                "mean": np.mean(valid_scores),
                "std": np.std(valid_scores),
                "step": Agents.current_step,
            }

            # 打印训练总体信息
            total_train_start_time = time.time()
            print("\n" + "=" * 40)
            print("训练信息摘要:")
            print(f"- 总训练步数: {train_steps}")
            print(f"- 总训练周期: {num_epoch}")
            print(f"- 每个周期步数: {eval_interval}")
            print(
                f"- 初始测试得分: 平均={np.mean(valid_scores):.4f}, 标准差={np.std(valid_scores):.4f}"
            )
            print("=" * 40 + "\n")

            # 训练循环
            for i_epoch in range(num_epoch):
                # 计算并显示整体进度
                overall_progress = (i_epoch / num_epoch) * 100
                print(
                    f"\n训练周期: {i_epoch + 1}/{num_epoch} (总进度: {overall_progress:.1f}%)"
                )

                # 如果不是第一个周期，计算预计剩余时间
                if i_epoch > 0:
                    elapsed_time = time.time() - total_train_start_time
                    estimated_total_time = elapsed_time / i_epoch * num_epoch
                    estimated_remaining_time = estimated_total_time - elapsed_time

                    # 转换为小时:分钟:秒格式
                    hours, remainder = divmod(estimated_remaining_time, 3600)
                    minutes, seconds = divmod(remainder, 60)
                    print(
                        f"预计剩余时间: {int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
                    )

                # 添加训练开始时间打印
                start_time = time.time()
                print(f"开始第 {i_epoch + 1} 个训练周期...")

                # 计算当前训练阶段的子区间，以便在长时间训练期间提供更多进度反馈
                # 例如，如果eval_interval是5000，我们可以将其分成5个小段，每1000步显示一次进度
                sub_intervals = 5  # 将每个训练周期分成的子区间数
                sub_interval_steps = max(
                    500, eval_interval // sub_intervals
                )  # 确保至少500步
                remaining_steps = eval_interval

                while remaining_steps > 0:
                    # 训练一个子区间
                    steps_to_train = min(sub_interval_steps, remaining_steps)
                    sub_start_time = time.time()
                    print(
                        f"  训练子阶段: 步数 {eval_interval - remaining_steps} - {eval_interval - remaining_steps + steps_to_train}"
                    )

                    # 进行训练
                    Agents.train(steps_to_train)

                    # 更新剩余步数
                    remaining_steps -= steps_to_train

                    # 计算并打印训练时间
                    sub_train_time = time.time() - sub_start_time
                    progress_percentage = (
                        100 * (eval_interval - remaining_steps) / eval_interval
                    )
                    print(
                        f"  完成 {progress_percentage:.1f}% - 当前步数: {Agents.current_step}, 用时: {sub_train_time:.2f}秒"
                    )

                # 计算并打印总训练时间
                train_time = time.time() - start_time
                print(f"训练周期 {i_epoch} 完成，用时 {train_time:.2f} 秒")
                print(f"当前总步数: {Agents.current_step}")

                # 进行测试并打印结果
                print("进行测试评估...")
                print("测试过程中没有详细的进度显示，请耐心等待...")
                # 使用XuanCe库原生测试方法
                test_scores = Agents.test(env_fn, test_episode)
                valid_scores = test_scores[test_episode:]
                print(
                    f"测试得分: 平均={np.mean(valid_scores):.4f}, 标准差={np.std(valid_scores):.4f}"
                )
                print(
                    f"当前最佳得分: 平均={best_scores_info['mean']:.4f}, 标准差={best_scores_info['std']:.4f}"
                )

                # 保存最佳模型
                if np.mean(valid_scores) > best_scores_info["mean"]:
                    best_scores_info = {
                        "mean": np.mean(valid_scores),
                        "std": np.std(valid_scores),
                        "step": Agents.current_step,
                    }
                    Agents.save_model(model_name="best_model.pth")  # 保存完整检查点
                    print(
                        f"✓ 发现更好的模型，已保存! 新得分: {best_scores_info['mean']:.4f}"
                    )

                # 打印分割线
                print("-" * 40)

            # 计算总训练时间
            total_train_time = time.time() - total_train_start_time
            hours, remainder = divmod(total_train_time, 3600)
            minutes, seconds = divmod(remainder, 60)

            print("\n" + "=" * 40)
            print("训练完成!")
            print(f"总训练时间: {int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}")
            print(
                f"最佳模型得分: {best_scores_info['mean']:.4f}, 标准差={best_scores_info['std']:.4f}"
            )
            print(f"最佳模型步数: {best_scores_info['step']}")
            print("=" * 40)

        else:
            # 测试模式
            if configs.test:

                def env_fn():
                    configs.parallels = configs.test_episode

                    return make_envs(configs)

                # 加载已训练的模型
                if hasattr(Agents, "model_dir_load") and Agents.model_dir_load:
                    print(
                        f"INFO: 测试模式 - 尝试从目录 '{Agents.model_dir_load}' 加载模型。"
                    )
                    Agents.load_model(path=Agents.model_dir_load)
                else:
                    print("WARNING: 测试模式 - model_dir_load 未设置，无法加载模型。")
                scores = Agents.test(env_fn, configs.test_episode)
                print(f"平均得分: {np.mean(scores):.2f}, 标准差: {np.std(scores):.2f}")
                print("测试完成！")

            # 训练模式
            else:
                Agents.train(configs.running_steps // configs.parallels)
                Agents.save_model("final_train_model.pth")
                print("训练完成！")

    except Exception as e:
        print(f"脚本执行过程中发生未捕获的异常: {e}")
        import traceback

        traceback.print_exc()

    finally:
        print("开始执行清理操作...")
        # 首先尝试调用 Agents.finish()
        if Agents is not None and hasattr(Agents, "finish"):
            print("调用 Agents.finish()...")
            Agents.finish()

        # 然后显式关闭环境
        if envs is not None and hasattr(envs, "close"):
            print("显式关闭环境...")
            envs.close()

        print("清理操作完成。脚本即将退出。")
