"""
运动显著性阈值（MST）多智能体环境基类
该环境封装sim_py仿真，作为强化学习训练的环境接口
"""

import copy

import numpy as np
from gym import spaces

from xuance.environment import RawMultiAgentEnv

# 确保能够正确导入sim_py_copy模块 (注：此段sys.path修改已移除，采用绝对导入)


class MSTEnv(RawMultiAgentEnv):
    """
    运动显著性阈值环境基类，封装sim_py仿真环境
    该类只定义通用接口，具体实现在各场景子类中
    """

    def __init__(self, config, max_episode_steps: int = 1000):
        """
        初始化MST环境

        参数:
            config: 环境配置，包含仿真参数和奖励设置，可以是dict或Namespace
            max_episode_steps (int): 最大步数
        """
        super(MSTEnv, self).__init__()

        self.config = config

        # 处理不同类型的配置对象
        if hasattr(config, "max_episode_steps"):
            self.max_episode_steps = config.max_episode_steps
        else:
            self.max_episode_steps = max_episode_steps

        self.episode_step = 0
        self.continuous_actions = True  # MST调节是连续动作

        # MST阈值调节的范围
        self.mst_min = getattr(config, "mst_min", 0.0)
        self.mst_max = getattr(config, "mst_max", 35.0)

        # 观察归一化参数
        self.normalize_obs = getattr(config, "normalize_obs", True)
        self.normalize_state = getattr(config, "normalize_state", True)

        # 拓扑邻居数量
        self.k_neighbors = getattr(config, "k_neighbors", 7)

        # 场景类型编码
        self.scenario_type_code = None  # 子类中设置

        # 仿真相关属性，在子类中初始化
        self.runtime_state = None

        # 强化学习相关属性
        self.n_agents = 0
        self.agents = []
        self.previous_actions = {}
        self.individual_episode_reward = {}

        # 构建观察和动作空间
        self._setup_spaces()

    def _setup_spaces(self):
        """
        设置观察空间和动作空间，在子类中可能需要重写
        """
        # 基础设置，子类可能需要扩展

        # 观察空间：
        # 自身状态(4) + 邻居统计(3) + 邻居详情(5*n_neighbors) + 场景信息(5)
        obs_dim = 4 + 3 + 5 * self.k_neighbors + 5
        self.observation_space = {}

        # 动作空间 (连续MST阈值)[0, 35.0]
        self.action_space = {}

        # 中心化Critic的状态空间 - 统一设置为20维
        # 不再根据场景类型区分维度，确保CE和FP环境返回相同维度的状态
        state_dim = 20  # 全局状态统一设置为20维

        # 修改状态空间为有界范围
        self.state_space = spaces.Box(low=-10.0, high=10.0, shape=(state_dim,))

    def reset(self):
        """
        重置环境

        返回:
            observations (dict): 各智能体的初始观察
            reset_info (dict): 重置信息
        """
        # 重置步数计数器
        self.episode_step = 0

        # 重置仿真环境(在子类中实现)
        self._reset_simulation()

        # 更新智能体列表
        self.agents = [str(i) for i in self.runtime_state.robots_list]
        self.n_agents = len(self.agents)

        # 初始化各智能体的奖励
        self.individual_episode_reward = {k: 0.0 for k in self.agents}

        # 初始化前一步的动作
        self.previous_actions = {
            k: np.array([self.runtime_state.actors[int(k)].cj_threshold])
            for k in self.agents
        }

        # 构建观察和动作空间 - 使用有界范围
        for agent_id in self.agents:
            self.observation_space[agent_id] = spaces.Box(
                low=-10.0, high=10.0, shape=(self._get_obs_dim(),)
            )
            self.action_space[agent_id] = spaces.Box(
                low=self.mst_min, high=self.mst_max, shape=(1,)
            )

        # 获取初始观察
        observations = self._get_observations()
        reset_info = {
            "infos": {},
            "individual_episode_rewards": self.individual_episode_reward,
        }

        return observations, reset_info

    def _reset_simulation(self):
        """
        重置仿真环境，在子类中实现
        """
        raise NotImplementedError("在子类中实现")

    def _get_obs_dim(self):
        """
        获取观察空间维度
        """
        return 4 + 3 + 5 * self.k_neighbors + 5

    def step(self, actions):
        """
        执行环境步进

        参数:
            actions (dict): 各智能体的动作

        返回:
            observations (dict): 下一状态的观察
            rewards (dict): 各智能体的奖励
            terminated (dict): 各智能体是否终止
            truncated (dict): 是否因达到最大步数而截断
            info (dict): 附加信息
        """
        # 保存当前动作
        self.previous_actions = copy.deepcopy(actions)

        # 应用动作，更新MST阈值
        self._apply_actions(actions)

        # 执行仿真步进
        self._simulation_step()

        # 获取新的观察
        observations = self._get_observations()

        # 计算奖励
        rewards = self._compute_rewards()

        # 更新个体累积奖励
        for k, v in rewards.items():
            self.individual_episode_reward[k] += v

        # 检查终止条件
        self.episode_step += 1
        terminated = self._get_termination()
        truncated = self.episode_step >= self.max_episode_steps

        # 打包状态信息
        info = {
            "infos": {},
            "individual_episode_rewards": self.individual_episode_reward,
            "episode_score": self.individual_episode_reward.copy(),  # 为了与xuance库兼容
        }

        return observations, rewards, terminated, truncated, info

    def _apply_actions(self, actions):
        """
        应用智能体动作到仿真环境，更新MST阈值

        参数:
            actions (dict): 智能体动作
        """
        for agent_id, action in actions.items():
            # 裁剪MST阈值到有效范围
            mst_value = np.clip(action[0], self.mst_min, self.mst_max)
            # 更新仿真中的MST阈值
            actual_id = int(agent_id)
            self.runtime_state.actors[actual_id].cj_threshold = mst_value

    def _simulation_step(self):
        """
        执行仿真步进，在子类中实现
        """
        raise NotImplementedError("在子类中实现")

    def _get_observations(self):
        """
        获取所有智能体的观察

        返回:
            observations (dict): 各智能体的观察
        """
        observations = {}
        for agent_id in self.agents:
            observations[agent_id] = self._get_agent_obs(agent_id)

        return observations

    def _get_agent_obs(self, agent_id):
        """
        获取单个智能体的观察

        参数:
            agent_id (str): 智能体ID

        返回:
            observation (np.ndarray): 智能体观察
        """
        # 在子类中实现具体观察构建
        raise NotImplementedError("在子类中实现")

    def _compute_rewards(self):
        """
        计算所有智能体的奖励

        返回:
            rewards (dict): 各智能体的奖励
        """
        # 在子类中实现具体奖励计算
        raise NotImplementedError("在子类中实现")

    def _get_termination(self):
        """
        检查是否达到终止条件

        返回:
            terminated (dict): 各智能体是否终止
        """
        # 默认实现，除非达到最大步数，否则不终止
        # 子类可以重写该方法以实现特定终止条件
        terminated = {agent_id: False for agent_id in self.agents}
        return terminated

    def state(self):
        """
        返回环境的全局状态，用于中心化Critic的输入

        返回:
            state (np.ndarray): 全局状态表示
        """
        # 在子类中实现具体全局状态构建
        raise NotImplementedError("在子类中实现")

    def agent_mask(self):
        """
        返回智能体掩码，指示哪些智能体当前有效

        返回:
            mask (dict): 智能体掩码
        """
        # 所有智能体均有效
        return {agent_id: True for agent_id in self.agents}

    def avail_actions(self):
        """
        返回各智能体的可用动作掩码
        对于连续动作空间，返回None

        返回:
            avail_actions (dict): 可用动作掩码
        """
        return None

    def close(self):
        """
        关闭环境
        """
        pass
